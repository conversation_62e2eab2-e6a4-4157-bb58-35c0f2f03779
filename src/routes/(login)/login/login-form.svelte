<script lang="ts">
	// import * as Form from '$lib/components/ui/form';
	// import { Input } from '$lib/components/ui/input';
	// import * as Alert from '$lib/components/ui/alert';

	import { formSchema, type FormSchema, type Response } from './schema';
	import SuperDebug, { type SuperValidated, type Infer, superForm } from 'sveltekit-superforms';
	import { Button, Label, Input, Alert, Spinner } from 'flowbite-svelte';
	import { zodClient } from 'sveltekit-superforms/adapters';

	import { Field, Control, FieldErrors } from 'formsnap';
	import {
		EyeOutline,
		EyeSlashOutline,
		UserOutline,
		LockOutline,
		ExclamationCircleSolid
	} from 'flowbite-svelte-icons';
	import { t } from '$src/lib/stores/i18n';

	export let data: SuperValidated<Infer<FormSchema>>;
	export let response: Response;

	const form = superForm(data, {
		validators: zodClient(formSchema)
	});

	const { form: formData, enhance, delayed } = form;

	// Client-side validation state for username
	let usernameValidationError = '';
	let usernameBlurred = false;

	// Client-side validation state for password
	let passwordValidationError = '';
	let passwordBlurred = false;

	// Password visibility toggle state
	let showPassword = false;

	// Username input handler with filtering and validation
	function handleUsernameInput(event: Event) {
		const target = event.target as HTMLInputElement;

		// Filter out non-alphanumeric characters, allowing dots, underscores, and hyphens. Do not allow multiple hyphens e.g., '--'
		const filteredValue = target.value
			.replace(/[^a-zA-Z0-9._-]/g, '')
			.replace(/--/g, '-')
			.replace(/\.\./g, '.')
			.replace(/__/g, '_')
			.replace(/^[._-]/, '');

		// Update form data with filtered value
		$formData.username = filteredValue;

		// Clear validation error when user types valid characters
		if (filteredValue.length > 0) {
			usernameValidationError = '';
		} else if (usernameBlurred) {
			// Show required error if field is empty and was blurred
			usernameValidationError = 'Username is required';
		}

		// Show error if invalid characters were filtered out
		if (filteredValue !== target.value && filteredValue.length > 0) {
			usernameValidationError =
				'Username must contain only letters and numbers, dots, underscores, or hyphens';
		}

		// Update the input value to reflect the filtered value
		target.value = filteredValue;
	}

	// Handle username blur event
	function handleUsernameBlur(event: Event) {
		const target = event.target as HTMLInputElement;
		usernameBlurred = true;

		if (target.value.trim() === '') {
			usernameValidationError = 'Username is required';
		}
	}

	// Handle password input event
	function handlePasswordInput(event: Event) {
		const target = event.target as HTMLInputElement;

		// Clear validation error when user types
		if (target.value.trim() !== '') {
			passwordValidationError = '';
		} else if (passwordBlurred) {
			// Show required error if field is empty and was blurred
			passwordValidationError = 'Password is required';
		}
	}

	// Handle password blur event
	// function handlePasswordBlur(event: Event) {
	// 	const target = event.target as HTMLInputElement;
	// 	passwordBlurred = true;

	// 	if (target.value.trim() === '') {
	// 		passwordValidationError = 'Password is required';
	// 	}
	// }

	// Handle paste events to filter pasted content
	function handleUsernamePaste(event: ClipboardEvent) {
		event.preventDefault();

		const pastedText = event.clipboardData?.getData('text') || '';

		// Filter out non-alphanumeric characters, allowing dots, underscores, and hyphens. Do not allow multiple hyphens e.g., '--'
		const filteredText = pastedText
			.replace(/[^a-zA-Z0-9._-]/g, '')
			.replace(/--/g, '-')
			.replace(/\.\./g, '.')
			.replace(/__/g, '_')
			.replace(/^[._-]/, '');

		// Update form data with filtered value
		$formData.username = filteredText;

		// Clear validation errors if we have valid content
		if (filteredText.length > 0) {
			usernameValidationError = '';
		}

		// Show validation message if content was filtered
		if (filteredText !== pastedText && filteredText.length > 0) {
			usernameValidationError =
				'Username must contain only letters and numbers, dots, underscores, or hyphens';
		}

		// Update the input element directly
		const target = event.target as HTMLInputElement;
		target.value = filteredText;

		// Trigger input event to ensure reactivity
		target.dispatchEvent(new Event('input', { bubbles: true }));
	}

	// Toggle password visibility
	function togglePasswordVisibility() {
		showPassword = !showPassword;
	}

	// Reactive statement for form validation state
	$: isUsernameValid = $formData.username.trim() !== '' && usernameValidationError === '';
	$: isPasswordValid = $formData.password.trim() !== '' && passwordValidationError === '';
	$: isFormValid = isUsernameValid && isPasswordValid;
</script>

<!-- <SuperDebug data={response} /> -->
<!-- Main Container -->
<!-- <br /> -->

<div class="row text-center">
	<!-- Left Side - Image -->
	<!-- <div class="column">
        <img src="/images/Salmate-Logo-Transparent.png" alt="Salmate Logo" class="w-64 h-64 object-contain">
    </div> -->

	<!-- Right Side - Login -->
	<div class="column">
		<form class="flex flex-col" action="?/login" method="POST" use:enhance>
			<h3 class="dark:text-white mb-5 text-center text-5xl font-bold text-gray-900">Salmate</h3>

			<br />
			<Field {form} name="username">
				<Control let:attrs>
					<Label class="mb-1 text-left" for="username">{t('login_username_label')}</Label>
					<div class="relative">
						<div class="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3.5">
							<UserOutline class="h-5 w-5 text-gray-500" />
						</div>
						<Input
							{...attrs}
							id="username"
							name="username"
							type="text"
							bind:value={$formData.username}
							placeholder={t('login_username_placeholder')}
							class="focus:ring-blue ps-10 focus:border-transparent focus:ring-2 {usernameValidationError
								? 'border-red-500 focus:ring-red-500'
								: ''}"
							on:input={handleUsernameInput}
							on:paste={handleUsernamePaste}
							maxlength={20}
							autofocus
						/>
					</div>
					{#if usernameValidationError}
						<Alert color="red" class="mt-1 px-3 py-1 text-left text-xs font-medium text-red-500">
							{usernameValidationError}
						</Alert>
					<!-- {:else}
						<FieldErrors /> -->
					{/if}
				</Control>
			</Field>
			<br />
			<Field {form} name="password">
				<Control let:attrs>
					<Label class="mb-1 text-left" for="password">{t('login_password_label')}</Label>
					<div class="relative">
						<div class="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3.5">
							<LockOutline class="h-5 w-5 text-gray-500" />
						</div>
						<Input
							{...attrs}
							id="password"
							name="password"
							type={showPassword ? 'text' : 'password'}
							bind:value={$formData.password}
							placeholder={t('login_password_placeholder')}
							class="focus:ring-blue pe-10 ps-10 focus:border-transparent focus:ring-2 {passwordValidationError
								? 'border-red-500 focus:ring-red-500'
								: ''}"
							on:input={handlePasswordInput}
							maxlength={50}
						/>
						<button
							type="button"
							class="absolute inset-y-0 end-0 flex cursor-pointer items-center pe-3.5 transition-colors hover:text-blue-500"
							on:click={togglePasswordVisibility}
							tabindex="-1"
							aria-label={showPassword ? 'Hide password' : 'Show password'}
						>
							{#if showPassword}
								<EyeSlashOutline class="h-5 w-5 text-gray-500 hover:text-blue-500" />
							{:else}
								<EyeOutline class="h-5 w-5 text-gray-500 hover:text-blue-500" />
							{/if}
						</button>
					</div>
					<!-- {#if passwordValidationError}
						<Alert color="red" class="mt-1 px-3 py-1 text-left text-xs">
							{passwordValidationError}
						</Alert>
					{:else}
						<FieldErrors />
					{/if} -->
				</Control>
			</Field>
			<br />
			<div class="flex flex-col gap-0">
				<Button
					type="submit"
					disabled={!isFormValid || $delayed}
					class="w-full bg-gradient-to-r from-cyan-400 to-sky-500 hover:from-cyan-500 hover:to-sky-600 {!isFormValid ||
					$delayed
						? 'cursor-not-allowed opacity-50'
						: ''}"
				>
					{#if $delayed}
						<Spinner class="me-3" size="4" color="white" data-testid="loading-spinner" /> Logging In
					{:else}
						Login
					{/if}
				</Button>
				{#if response?.form?.message?.status == 'fail'}
					<Alert
						color="red"
						class="mt-1 flex justify-center gap-1 px-3 py-1 text-xs font-medium text-red-500"
					>
						<ExclamationCircleSolid class="h-4 w-4" />
						{response?.form?.message?.detail || 'Invalid username or password'}
					</Alert>
				{/if}
			</div>
		</form>
	</div>
</div>
